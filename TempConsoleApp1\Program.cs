﻿using NPOI.SS.UserModel;

namespace TempConsoleApp1
{
    internal class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== CellValueResult 功能演示 ===");
            Console.WriteLine();

            // 演示内存中创建的不同类型数据
            TypeDemo.DemonstrateTypes();

            Console.WriteLine();
            Console.WriteLine("=== 从文件读取演示 ===");

            // 如果存在 Excel 文件，则读取并显示
            string filePath = "npoi读取.xlsx";
            if (File.Exists(filePath))
            {
                ReadExcelFile(filePath);
            }
            else
            {
                Console.WriteLine($"文件 {filePath} 不存在，跳过文件读取演示。");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 读取 Excel 文件并显示单元格类型信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        private static void ReadExcelFile(string filePath)
        {
            FileStream? fileStream = null;
            IWorkbook? workbook = null;

            try
            {
                Console.WriteLine($"读取文件: {filePath}");
                fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                workbook = new NPOI.XSSF.UserModel.XSSFWorkbook(fileStream);
                ISheet sheet = workbook.GetSheetAt(0);

                // 读取前几行数据作为演示
                for (int rowIndex = 0; rowIndex < Math.Min(5, sheet.LastRowNum + 1); rowIndex++)
                {
                    var row = sheet.GetRow(rowIndex);
                    if (row == null) continue;

                    Console.WriteLine($"第 {rowIndex + 1} 行:");
                    for (int colIndex = 0; colIndex < Math.Min(5, row.LastCellNum); colIndex++)
                    {
                        var cell = row.GetCell(colIndex);
                        var result = ExcelHelper.NPOIGetValueByCellType(cell);

                        Console.WriteLine($"  列 {colIndex + 1}: 类型={result.ValueType?.Name ?? "null"}, 值={result.Value}, NPOI类型={result.OriginalCellType}");
                    }
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件时出错: {ex.Message}");
            }
            finally
            {
                workbook?.Close();
                workbook?.Dispose();
                fileStream?.Dispose();
            }

            //Application? excelApp = null;
            //Workbook? workbookCom = null;
            //try
            //{
            //    Console.WriteLine("COM获得值如下：");
            //    excelApp = new Application();
            //    workbookCom = excelApp.Workbooks.Open(filePath);
            //    Worksheet? worksheet = workbookCom.Worksheets[1] as Worksheet;
            //    for (int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++)
            //    {
            //        var cell = worksheet?.Cells[rowIndex + 1, columnIndex + 1] as Microsoft.Office.Interop.Excel.Range;
            //        Console.WriteLine(ExcelHelper.COMGetCellValue(cell));
            //    }
            //}
            //finally
            //{
            //    workbookCom?.Close();
            //    excelApp?.Quit();
            //    if (workbookCom != null)
            //    {
            //        Marshal.ReleaseComObject(workbookCom);
            //    }
            //    if (excelApp !=null)
            //    {
            //        Marshal.ReleaseComObject(excelApp);
            //    }
            //}
        }
    }
}
