﻿using Microsoft.Office.Interop.Excel;
using NPOI.SS.UserModel;
using System.Runtime.InteropServices;

namespace TempConsoleApp1
{
    internal class Program
    {
        static void Main(string[] args)
        {
            FileStream fileStream = null;
            IWorkbook workbook = null;

            string filePath = "D:\\Projects\\CSharp\\TempConsoleApp1\\npoi读取.xlsx";
            bool isXLSX = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);
            int startRowIndex = 20; //6
            int endRowIndex = 20; //24
            int columnIndex = 4;
            try
            {
                Console.WriteLine("NPOI获得值如下：");
                fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                workbook = isXLSX ? new NPOI.XSSF.UserModel.XSSFWorkbook(fileStream) : new NPOI.HSSF.UserModel.HSSFWorkbook(fileStream);
                ISheet sheet = workbook.GetSheetAt(0);
                for (int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++)
                {
                    var row = sheet.GetRow(rowIndex);
                    if (row == null)
                    {
                        continue;
                    }
                    var cell = row.GetCell(columnIndex);
                    Console.WriteLine(ExcelHelper.NPOIGetValueByCellType(cell));
                }
            }
            finally
            {
                workbook?.Close();
                workbook?.Dispose();
                fileStream?.Dispose();
            }

            //Application? excelApp = null;
            //Workbook? workbookCom = null;
            //try
            //{
            //    Console.WriteLine("COM获得值如下：");
            //    excelApp = new Application();
            //    workbookCom = excelApp.Workbooks.Open(filePath);
            //    Worksheet? worksheet = workbookCom.Worksheets[1] as Worksheet;
            //    for (int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++)
            //    {
            //        var cell = worksheet?.Cells[rowIndex + 1, columnIndex + 1] as Microsoft.Office.Interop.Excel.Range;
            //        Console.WriteLine(ExcelHelper.COMGetCellValue(cell));
            //    }
            //}
            //finally
            //{
            //    workbookCom?.Close();
            //    excelApp?.Quit();
            //    if (workbookCom != null)
            //    {
            //        Marshal.ReleaseComObject(workbookCom);
            //    }
            //    if (excelApp !=null)
            //    {
            //        Marshal.ReleaseComObject(excelApp);
            //    }
            //}
        }
    }
}
