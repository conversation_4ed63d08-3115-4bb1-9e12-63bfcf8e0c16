﻿using NPOI.SS.UserModel;

namespace TempConsoleApp1
{
    internal class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 优化后的 ExcelHelper 功能演示 ===");
            Console.WriteLine();

            // 演示内存中创建的不同类型数据
            DemonstrateInMemoryTypes();

            Console.WriteLine();
            Console.WriteLine("=== 从文件读取演示 ===");

            // 如果存在 Excel 文件，则读取并显示
            string filePath = "npoi读取.xlsx";
            if (File.Exists(filePath))
            {
                ReadExcelFile(filePath);
            }
            else
            {
                Console.WriteLine($"文件 {filePath} 不存在，跳过文件读取演示。");
            }

            Console.WriteLine();
            Console.WriteLine("=== 性能测试 ===");
            PerformanceTest();

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 读取 Excel 文件并显示单元格类型信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        private static void ReadExcelFile(string filePath)
        {
            FileStream? fileStream = null;
            IWorkbook? workbook = null;
            var startTime = DateTime.Now;

            try
            {
                Console.WriteLine($"读取文件: {filePath}");
                fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                workbook = new NPOI.XSSF.UserModel.XSSFWorkbook(fileStream);
                ISheet sheet = workbook.GetSheetAt(0);

                // 显示工作簿信息（使用缓存）
                Console.WriteLine($"工作簿日期系统: {(ExcelHelper.Is1904DateSystem(workbook) ? "1904" : "1900")}");
                Console.WriteLine();

                int cellCount = 0;

                // 读取前几行数据作为演示
                for (int rowIndex = 0; rowIndex < Math.Min(5, sheet.LastRowNum + 1); rowIndex++)
                {
                    var row = sheet.GetRow(rowIndex);
                    if (row == null) continue;

                    Console.WriteLine($"第 {rowIndex + 1} 行:");
                    for (int colIndex = 0; colIndex < Math.Min(5, row.LastCellNum); colIndex++)
                    {
                        var cell = row.GetCell(colIndex);
                        var result = ExcelHelper.NPOIGetValueByCellType(cell);
                        cellCount++;

                        Console.WriteLine($"  列 {colIndex + 1}: 类型={result.ValueType?.Name ?? "null"}, 值={result.Value}, NPOI类型={result.OriginalCellType}");
                    }
                    Console.WriteLine();
                }

                var duration = DateTime.Now - startTime;
                Console.WriteLine($"处理了 {cellCount} 个单元格，耗时: {duration.TotalMilliseconds:F2}ms");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件时出错: {ex.Message}");
            }
            finally
            {
                // 清理缓存
                if (workbook != null)
                {
                    ExcelHelper.ClearWorkbookCache(workbook);
                }

                workbook?.Close();
                workbook?.Dispose();
                fileStream?.Dispose();
            }

        }

        /// <summary>
        /// 演示内存中创建的不同类型数据
        /// </summary>
        private static void DemonstrateInMemoryTypes()
        {
            Console.WriteLine("=== 内存数据类型演示 ===");

            // 创建一个内存中的工作簿
            var workbook = new NPOI.XSSF.UserModel.XSSFWorkbook();
            var sheet = workbook.CreateSheet("Demo");
            var row = sheet.CreateRow(0);

            // 创建不同类型的单元格
            var cells = new (string desc, Action<ICell> setup)[]
            {
                ("字符串", cell => cell.SetCellValue("Hello World")),
                ("数字", cell => cell.SetCellValue(123.45)),
                ("布尔", cell => cell.SetCellValue(true)),
                ("日期", cell => {
                    cell.SetCellValue(DateTime.Now);
                    var style = workbook.CreateCellStyle();
                    var format = workbook.CreateDataFormat();
                    style.DataFormat = format.GetFormat("yyyy-mm-dd");
                    cell.CellStyle = style;
                }),
                ("时间", cell => {
                    cell.SetCellValue(DateTime.Now);
                    var style = workbook.CreateCellStyle();
                    var format = workbook.CreateDataFormat();
                    style.DataFormat = format.GetFormat("hh:mm:ss");
                    cell.CellStyle = style;
                }),
                ("公式", cell => cell.SetCellFormula("B1*2"))
            };

            for (int i = 0; i < cells.Length; i++)
            {
                var cell = row.CreateCell(i);
                cells[i].setup(cell);

                var result = ExcelHelper.NPOIGetValueByCellType(cell);
                Console.WriteLine($"{cells[i].desc}: 类型={result.ValueType?.Name ?? "null"}, 值={result.Value}, NPOI类型={result.OriginalCellType}");
            }

            // 清理
            ExcelHelper.ClearWorkbookCache(workbook);
            workbook.Close();
            Console.WriteLine();
        }

        /// <summary>
        /// 性能测试
        /// </summary>
        private static void PerformanceTest()
        {
            Console.WriteLine("创建大量单元格进行性能测试...");

            var workbook = new NPOI.XSSF.UserModel.XSSFWorkbook();
            var sheet = workbook.CreateSheet("Performance");

            const int testRows = 100;
            const int testCols = 10;

            // 创建测试数据
            for (int r = 0; r < testRows; r++)
            {
                var row = sheet.CreateRow(r);
                for (int c = 0; c < testCols; c++)
                {
                    var cell = row.CreateCell(c);
                    // 创建不同类型的数据
                    switch (c % 4)
                    {
                        case 0: cell.SetCellValue($"Text_{r}_{c}"); break;
                        case 1: cell.SetCellValue(r * c + 0.5); break;
                        case 2: cell.SetCellValue(r % 2 == 0); break;
                        case 3: cell.SetCellValue(DateTime.Now.AddDays(r)); break;
                    }
                }
            }

            // 性能测试
            var startTime = DateTime.Now;
            int processedCells = 0;

            for (int r = 0; r < testRows; r++)
            {
                var row = sheet.GetRow(r);
                for (int c = 0; c < testCols; c++)
                {
                    var cell = row.GetCell(c);
                    var result = ExcelHelper.NPOIGetValueByCellType(cell);
                    processedCells++;
                }
            }

            var duration = DateTime.Now - startTime;
            Console.WriteLine($"处理了 {processedCells} 个单元格");
            Console.WriteLine($"总耗时: {duration.TotalMilliseconds:F2}ms");
            Console.WriteLine($"平均每个单元格: {duration.TotalMilliseconds / processedCells:F4}ms");

            // 清理
            ExcelHelper.ClearWorkbookCache(workbook);
            workbook.Close();
        }
    }
}
