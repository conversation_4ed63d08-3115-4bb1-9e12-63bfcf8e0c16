﻿using NPOI.SS.UserModel;
using System.Text.RegularExpressions;
using DateUtil = NPOI.SS.UserModel.DateUtil;

namespace TempConsoleApp1
{
    public enum TemporalKind { None, DateOnly, TimeOnly, DateTime }

    public sealed record TemporalResult(TemporalKind Kind, DateOnly? DateOnly, TimeOnly? TimeOnly, DateTime? DateTime);

    public static class NpoiTemporalHelper
    {
        // 预编译的正则表达式以提升性能
        private static readonly Regex _quotedTextRegex = new("\"[^\"]*\"", RegexOptions.Compiled);
        private static readonly Regex _escapedCharRegex = new(@"\\.", RegexOptions.Compiled);
        private static readonly Regex _localeMarkerRegex = new(@"\[\$-[^\]]*\]", RegexOptions.Compiled);
        private static readonly Regex _nonTimeSquareBracketRegex = new(@"\[(?![hms])[^]]+\]", RegexOptions.Compiled);
        private static readonly Regex _dateTimeTokenRegex = new(@"y+|d+|h+|s+|m+|am\/pm|a\/p|\[h\]|\[m\]|\[s\]", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _yearTokenRegex = new("y+", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _dayTokenRegex = new(@"d+", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _hourTokenRegex = new(@"(\[h+\]|h+)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _secondTokenRegex = new(@"(\[s+\]|s+)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _amPmTokenRegex = new(@"am\/pm|a\/p", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _minuteTokenRegex = new("m+", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex _minuteContextRegex = new(@"h+.*m+|m+.*s+", RegexOptions.Compiled | RegexOptions.IgnoreCase);

        // 内置日期格式索引（仅包含确认的日期格式，移除 23-36 保留范围以避免误判）
        private static readonly HashSet<short> _builtInDateIndexes = new()
        {
            14, 15, 16, 17, 18, 19, 20, 21, 22 // 标准日期格式
        };

        // 内置时间格式索引（移除可能的时长格式，避免与时间点混淆）
        private static readonly HashSet<short> _builtInTimeIndexes = new()
        {
            18, 19, 20, 21 // 标准时间格式，移除 45, 46, 47（可能是时长格式）
        };
        /// <summary>
        /// 尝试从 NPOI 单元格中提取日期/时间数据
        /// </summary>
        /// <param name="cell">NPOI 单元格对象</param>
        /// <param name="result">提取结果</param>
        /// <returns>是否成功提取到日期/时间数据</returns>
        public static bool TryGetTemporal(ICell cell, out TemporalResult result)
        {
            result = new TemporalResult(TemporalKind.None, null, null, null);

            if (!IsValidCell(cell, out var cellInfo))
                return false;

            if (!IsDateTimeFormat(cellInfo.FormatIndex, cellInfo.FormatString, cellInfo.CleanedFormatString))
                return false;

            // 一次性分析格式，避免重复计算
            var (hasDate, hasTime) = AnalyzeFormatTokens(cellInfo.CleanedFormatString, cellInfo.FormatIndex);

            // 优先尝试直接访问 NPOI 2.7+ 的属性
            if (TryGetTemporalFromDirectAccess(cell, hasDate, hasTime, out result))
                return true;

            // 兜底方案：使用底层转换
            bool use1904 = Is1904DateSystem(cell);
            return TryGetTemporalFromFallback(cellInfo.NumericValue, hasDate, hasTime, use1904, out result);
        }

        /// <summary>
        /// 验证单元格是否有效并提取基本信息
        /// </summary>
        private static bool IsValidCell(ICell cell, out CellInfo cellInfo)
        {
            cellInfo = default;

            if (cell == null)
                return false;

            // 处理公式单元格：使用缓存结果类型
            CellType cellType = cell.CellType == CellType.Formula ? cell.CachedFormulaResultType : cell.CellType;
            if (cellType != CellType.Numeric)
                return false;

            double numericValue = cell.NumericCellValue;
            ICellStyle style = cell.CellStyle;
            short formatIndex = style?.DataFormat ?? 0;
            string formatString = style?.GetDataFormatString() ?? string.Empty;

            cellInfo = new CellInfo(numericValue, formatIndex, formatString);
            return true;
        }

        /// <summary>
        /// 判断格式是否为日期/时间格式
        /// </summary>
        private static bool IsDateTimeFormat(short formatIndex, string formatString, string cleanedFormat)
        {
            // 1. 使用 NPOI 的权威判定
            if (DateUtil.IsADateFormat(formatIndex, formatString))
                return true;

            // 2. 检查内置日期格式索引
            if (_builtInDateIndexes.Contains(formatIndex))
                return true;

            // 3. 通过格式字符串模式匹配判定
            return ContainsDateTimeTokens(cleanedFormat);
        }

        /// <summary>
        /// 获取工作簿的日期系统（1900 或 1904）- 使用 ExcelHelper 的缓存版本
        /// </summary>
        private static bool Is1904DateSystem(ICell cell)
        {
            return ExcelHelper.Is1904DateSystem(cell);
        }

        /// <summary>
        /// 尝试直接访问 NPOI 2.7+ 的日期/时间属性
        /// </summary>
        private static bool TryGetTemporalFromDirectAccess(ICell cell, bool hasDate, bool hasTime, out TemporalResult result)
        {
            result = new TemporalResult(TemporalKind.None, null, null, null);

            try
            {
                // 使用通用 DateTime 属性，根据格式分析结果决定返回类型
                // 这样确保了格式分析结果与返回类型的一致性
                if (cell.DateCellValue is DateTime dateTime)
                {
                    if (hasDate && hasTime)
                    {
                        result = new TemporalResult(TemporalKind.DateTime, null, null, dateTime);
                    }
                    else if (hasDate)
                    {
                        result = new TemporalResult(TemporalKind.DateOnly, DateOnly.FromDateTime(dateTime), null, null);
                    }
                    else
                    {
                        result = new TemporalResult(TemporalKind.TimeOnly, null, TimeOnly.FromDateTime(dateTime), null);
                    }

                    return true;
                }
            }
            catch
            {
                // 属性访问失败时继续使用兜底方案
            }

            return false;
        }

        /// <summary>
        /// 兜底方案：使用底层转换获取日期/时间
        /// </summary>
        private static bool TryGetTemporalFromFallback(double numericValue, bool hasDate, bool hasTime, bool use1904, out TemporalResult result)
        {
            result = new TemporalResult(TemporalKind.None, null, null, null);

            // 验证数值是否为有效的 Excel 日期，或者是纯时间值（0-1之间）
            bool isValidDate = DateUtil.IsValidExcelDate(numericValue) || (numericValue > -1 && numericValue < 1);
            if (!isValidDate)
                return false;

            DateTime dateTime = DateUtil.GetJavaDate(numericValue, use1904);

            if (hasDate && hasTime)
            {
                result = new TemporalResult(TemporalKind.DateTime, null, null, dateTime);
            }
            else if (hasDate)
            {
                result = new TemporalResult(TemporalKind.DateOnly, DateOnly.FromDateTime(dateTime), null, null);
            }
            else
            {
                // 纯时间：从小数部分计算时间，使用 Floor 避免负数问题
                double fraction = numericValue - Math.Floor(numericValue);

                // 基于 tick 四舍五入，避免 59.999... 秒的显示问题
                long ticks = (long)Math.Round(fraction * TimeSpan.TicksPerDay);
                TimeOnly timeOnly = TimeOnly.FromTimeSpan(TimeSpan.FromTicks(ticks));
                result = new TemporalResult(TemporalKind.TimeOnly, null, timeOnly, null);
            }

            return true;
        }

        /// <summary>
        /// 检查格式字符串是否包含日期/时间标记
        /// </summary>
        private static bool ContainsDateTimeTokens(string cleanedFormat)
        {
            if (string.IsNullOrEmpty(cleanedFormat))
                return false;

            return _dateTimeTokenRegex.IsMatch(cleanedFormat);
        }

        /// <summary>
        /// 分析格式字符串中的日期和时间标记
        /// </summary>
        private static (bool hasDate, bool hasTime) AnalyzeFormatTokens(string cleanedFormatString, short formatIndex)
        {
            bool hasYear = _yearTokenRegex.IsMatch(cleanedFormatString);
            bool hasDay = _dayTokenRegex.IsMatch(cleanedFormatString);
            bool hasHour = _hourTokenRegex.IsMatch(cleanedFormatString);
            bool hasSecond = _secondTokenRegex.IsMatch(cleanedFormatString);
            bool hasAmPm = _amPmTokenRegex.IsMatch(cleanedFormatString);

            // 精确判断 m 是月份还是分钟
            bool hasMinuteToken = _minuteTokenRegex.IsMatch(cleanedFormatString);
            bool isMinuteContext = hasAmPm || hasHour || hasSecond || cleanedFormatString.Contains(':') || _minuteContextRegex.IsMatch(cleanedFormatString);

            bool hasMonth = hasMinuteToken && !isMinuteContext;
            bool hasMinute = hasMinuteToken && isMinuteContext;

            bool hasDate = hasYear || hasDay || hasMonth || _builtInDateIndexes.Contains(formatIndex);
            bool hasTime = hasHour || hasSecond || hasMinute || _builtInTimeIndexes.Contains(formatIndex);

            return (hasDate, hasTime);
        }

        /// <summary>
        /// 清理格式字符串，移除引号、转义字符和区域标记
        /// </summary>
        private static string CleanFormatString(string formatString)
        {
            if (string.IsNullOrEmpty(formatString))
                return string.Empty;

            // 转换为小写并取第一个分号前的部分
            string cleaned = formatString.ToLowerInvariant();
            int semicolonIndex = cleaned.IndexOf(';');
            if (semicolonIndex >= 0)
                cleaned = cleaned.Substring(0, semicolonIndex);

            // 移除各种格式标记
            cleaned = _quotedTextRegex.Replace(cleaned, "");           // 移除 "..." 字面量
            cleaned = _escapedCharRegex.Replace(cleaned, "");          // 移除转义字符
            cleaned = _localeMarkerRegex.Replace(cleaned, "");         // 移除区域标记 [$-...]
            cleaned = _nonTimeSquareBracketRegex.Replace(cleaned, ""); // 移除非时间的方括号标记

            return cleaned;
        }

        /// <summary>
        /// 单元格信息结构体
        /// </summary>
        private readonly struct CellInfo
        {
            public readonly double NumericValue;
            public readonly short FormatIndex;
            public readonly string FormatString;
            public readonly string CleanedFormatString;

            public CellInfo(double numericValue, short formatIndex, string formatString)
            {
                NumericValue = numericValue;
                FormatIndex = formatIndex;
                FormatString = formatString;
                CleanedFormatString = CleanFormatString(formatString);
            }
        }
    }
}
