using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Globalization;
using System.Text.RegularExpressions;
using Excel = Microsoft.Office.Interop.Excel;

namespace TempConsoleApp1
{
    /// <summary>
    /// 单元格值结果
    /// </summary>
    /// <param name="CellType">单元格类型</param>
    /// <param name="Value">单元格值</param>
    public sealed record CellValueResult(CellType CellType, object? Value);

    public static class ExcelHelper
    {
        /// <summary>
        /// NPOI通过单元格类型获取单元格值
        /// </summary>
        /// <param name="cell">NPOI单元格对象</param>
        /// <param name="getFormulaValue">是否获取公式计算结果值，false则返回公式字符串</param>
        /// <returns>包含单元格类型和值的结果对象</returns>
        public static CellValueResult NPOIGetValueByCellType(ICell cell, bool getFormulaValue = true)
        {
            if (cell == null)
            {
                return new CellValueResult(CellType.Blank, null);
            }

            var cellType = cell.CellType;
            object? value = cellType switch
            {
                CellType.Boolean => cell.BooleanCellValue,
                CellType.Numeric => LooksLikeDateOrTime(cell, out var formattedValue)
                    ? formattedValue
                    : cell.NumericCellValue,
                CellType.String => cell.StringCellValue,
                CellType.Error => GetErrorValue(cell),
                CellType.Formula => getFormulaValue
                    ? GetValueByCellFormulaResultType(cell).Value
                    : cell.CellFormula,
                CellType.Unknown or CellType.Blank or _ => null
            };

            return new CellValueResult(cellType, value);
        }

        /// <summary>
        /// 获取错误单元格的错误信息
        /// </summary>
        /// <param name="cell">单元格对象</param>
        /// <returns>错误信息字符串</returns>
        private static string GetErrorValue(ICell cell)
        {
            if (cell is XSSFCell xSSFCell)
            {
                return xSSFCell.ErrorCellString;
            }
            else
            {
                byte errorCode = cell.ErrorCellValue;
                return FormulaError.ForInt(errorCode).String;
            }
        }

        /// <summary>
        /// NPOI公式单元格获取值
        /// </summary>
        /// <param name="cell">单元格对象</param>
        /// <returns>包含单元格类型和值的结果对象</returns>
        private static CellValueResult GetValueByCellFormulaResultType(ICell cell)
        {
            if (cell == null)
            {
                return new CellValueResult(CellType.Blank, null);
            }

            var cachedResultType = cell.CachedFormulaResultType;
            object? value = cachedResultType switch
            {
                CellType.Boolean => cell.BooleanCellValue,
                CellType.Numeric => LooksLikeDateOrTime(cell, out var formattedValue)
                    ? formattedValue
                    : cell.NumericCellValue,
                CellType.String => cell.StringCellValue,
                CellType.Error => GetErrorValue(cell),
                CellType.Unknown or CellType.Blank or CellType.Formula or _ => null
            };

            return new CellValueResult(cachedResultType, value);
        }

        private static bool LooksLikeDateOrTime(ICell cell, out object? formattedValue)
        {
            formattedValue = null;
            if (!NpoiTemporalHelper.TryGetTemporal(cell, out var r))
            {
                return false;
            }

            switch (r.Kind)
            {
                case TemporalKind.DateOnly:
                    formattedValue = r.DateOnly;
                    break;
                case TemporalKind.TimeOnly:
                    formattedValue = r.TimeOnly;
                    break;
                case TemporalKind.DateTime:
                    formattedValue = r.DateTime;
                    break;
                default:
                    formattedValue = null;
                    return false;
            }

            return true;
        }
    }
}
