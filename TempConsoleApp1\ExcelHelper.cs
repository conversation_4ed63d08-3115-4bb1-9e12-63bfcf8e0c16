using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Globalization;
using System.Text.RegularExpressions;
using Excel = Microsoft.Office.Interop.Excel;

namespace TempConsoleApp1
{
    public static class ExcelHelper
    {
        /// <summary>
        /// NPOI通过单元格类型获取单元格值
        /// </summary>
        public static object? NPOIGetValueByCellType(ICell cell, bool getFormulaValue = true)
        {
            if (cell == null)
            {
                return null;
            }
            switch (cell.CellType)
            {
                case CellType.Boolean:
                    return cell.BooleanCellValue;
                case CellType.Numeric:
                    if (LooksLikeDateOrTime(cell, out var formattedValue)) // 日期类型
                    {
                        return formattedValue;
                    }
                    else // 其他数字类型
                    {
                        return cell.NumericCellValue;
                    }
                case CellType.String:
                    return cell.StringCellValue;
                case CellType.Error:
                    // ErrorCellValue 是 Error code，这里应该返回 Error string 
                    // 参见 https://poi.apache.org/apidocs/dev/org/apache/poi/xssf/usermodel/XSSFCell

                    XSSFCell xSSFCell = cell as XSSFCell;
                    if (null != xSSFCell)
                    {
                        return xSSFCell.ErrorCellString;
                    }
                    else
                    {
                        byte errorCode = cell.ErrorCellValue;
                        return FormulaError.ForInt(errorCode).String; // 获取错误信息字符串
                    }
                case CellType.Formula:
                    return getFormulaValue ? GetValueByCellFormulaResultType(cell) : cell.CellFormula;
                case CellType.Unknown:
                case CellType.Blank:
                default:
                    return null; // 空白单元格
            }
        }

        /// <summary>
        /// NPOI公式单元格获取值
        /// </summary>
        private static object? GetValueByCellFormulaResultType(ICell cell)
        {
            if (cell == null)
            {
                return null;
            }
            switch (cell.CachedFormulaResultType)
            {
                case CellType.Boolean:
                    return cell.BooleanCellValue;
                case CellType.Numeric:
                    if (LooksLikeDateOrTime(cell, out var formattedValue)) // 日期类型
                    {
                        return formattedValue;
                    }
                    else // 其他数字类型
                    {
                        return cell.NumericCellValue;
                    }
                case CellType.String:
                    return cell.StringCellValue;
                case CellType.Error:
                    // ErrorCellValue 是 Error code，这里应该返回 Error string 
                    // 参见 https://poi.apache.org/spreadsheet/dev/org/apache/poi/xssf/usermodel/XSSFCell.html

                    XSSFCell xSSFCell = cell as XSSFCell;
                    if (null != xSSFCell)
                    {
                        return xSSFCell.ErrorCellString;
                    }
                    else
                    {
                        byte errorCode = cell.ErrorCellValue;
                        return FormulaError.ForInt(errorCode).String; // 获取错误信息字符串
                    }
                case CellType.Unknown:
                case CellType.Blank:
                case CellType.Formula:
                default:
                    return null; // 空白单元格或未知类型
            }
        }

        private static bool LooksLikeDateOrTime(ICell cell, out object? formattedValue)
        {
            formattedValue = null;
            if (!NpoiTemporalHelper.TryGetTemporal(cell, out var r))
            {
                return false;
            }

            switch (r.Kind)
            {
                case TemporalKind.DateOnly:
                    formattedValue = r.DateOnly;
                    break;
                case TemporalKind.TimeOnly:
                    formattedValue = r.TimeOnly;
                    break;
                case TemporalKind.DateTime:
                    formattedValue = r.DateTime;
                    break;
                default:
                    formattedValue = null;
                    return false;
            }

            return true;
        }
    }
}
